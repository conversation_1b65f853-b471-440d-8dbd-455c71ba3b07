---
apiVersion: v1
kind: ConfigMap
metadata:
  name: deer-flow-config
  namespace: default
data:
  conf.yaml: |
    # [!NOTE]
    # Read the `docs/configuration_guide.md` carefully, and update the
    # configurations to match your specific settings and requirements.
    # - Replace `api_key` with your own credentials.
    # - Replace `base_url` and `model` name if you want to use a custom model.
    # - A restart is required every time you change the `config.yaml` file.

    BASIC_MODEL:
      base_url: https://ark.cn-beijing.volces.com/api/v3
      model: "doubao-1-5-pro-32k-250115"
      api_key: xxxx

    # Reasoning model is optional.
    # Uncomment the following settings if you want to use reasoning model
    # for planning.

    # REASONING_MODEL:
    #   base_url: https://ark-cn-beijing.bytedance.net/api/v3
    #   model: "doubao-1-5-thinking-pro-m-250428"
    #   api_key: xxxx

---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: deer-flow
  namespace: default
  labels:
    app: deer-flow
spec:
  selector:
    matchLabels:
      app: deer-flow
  template:
    metadata:
      labels:
        app: deer-flow
    spec:
      containers:
      - name: deer-flow
        image: deer-flow:latest  # 替换为您的镜像名称和标签
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8000
          name: api
        - containerPort: 3000
          name: web
        env:
        # Application Settings
        - name: DEBUG
          value: "True"
        - name: APP_ENV
          value: "development"
        
        # Docker build args
        - name: NEXT_PUBLIC_API_URL
          value: "http://localhost:8000/api"
        
        - name: AGENT_RECURSION_LIMIT
          value: "30"
        
        # Search Engine Settings
        - name: SEARCH_API
          value: "tavily"
        - name: TAVILY_API_KEY
          value: "tvly-xxx"  # 请替换为实际的API密钥
        
        # Optional: Brave Search (uncomment if needed)
        # - name: BRAVE_SEARCH_API_KEY
        #   value: "xxx"
        
        # Optional: Jina API (uncomment if needed)
        # - name: JINA_API_KEY
        #   value: "jina_xxx"
        
        # Optional: RAG provider (uncomment if needed)
        # - name: RAG_PROVIDER
        #   value: "ragflow"
        # - name: RAGFLOW_API_URL
        #   value: "http://localhost:9388"
        # - name: RAGFLOW_API_KEY
        #   value: "ragflow-xxx"
        # - name: RAGFLOW_RETRIEVAL_SIZE
        #   value: "10"
        
        # Optional: Volcengine TTS (uncomment if needed)
        # - name: VOLCENGINE_TTS_APPID
        #   value: "xxx"
        # - name: VOLCENGINE_TTS_ACCESS_TOKEN
        #   value: "xxx"
        # - name: VOLCENGINE_TTS_CLUSTER
        #   value: "volcano_tts"
        # - name: VOLCENGINE_TTS_VOICE_TYPE
        #   value: "BV700_V2_streaming"
        
        # Optional: LangSmith tracing (uncomment if needed)
        # - name: LANGSMITH_TRACING
        #   value: "true"
        # - name: LANGSMITH_ENDPOINT
        #   value: "https://api.smith.langchain.com"
        # - name: LANGSMITH_API_KEY
        #   value: "xxx"
        # - name: LANGSMITH_PROJECT
        #   value: "xxx"
        
        volumeMounts:
        - name: config-volume
          mountPath: /app/conf.yaml
          subPath: conf.yaml
          readOnly: true
        
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        
        # 健康检查
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      
      volumes:
      - name: config-volume
        configMap:
          name: deer-flow-config
      
      # 节点选择器（可选）
      # nodeSelector:
      #   kubernetes.io/os: linux
      
      # 容忍度（可选，允许在特定节点上调度）
      # tolerations:
      # - key: "node-role.kubernetes.io/master"
      #   operator: "Exists"
      #   effect: "NoSchedule"
      
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: deer-flow-service
  namespace: default
  labels:
    app: deer-flow
spec:
  type: NodePort  # 或者使用 ClusterIP, LoadBalancer
  ports:
  - name: api
    port: 8000
    targetPort: 8000
    nodePort: 30800  # 可选，指定NodePort端口
  - name: web
    port: 3000
    targetPort: 3000
    nodePort: 30300  # 可选，指定NodePort端口
  selector:
    app: deer-flow
